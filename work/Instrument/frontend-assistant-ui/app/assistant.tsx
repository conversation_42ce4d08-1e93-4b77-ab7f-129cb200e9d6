"use client";

import { useEffect, useMemo, useState } from "react";
import { AssistantRuntimeProvider, useLocalRuntime, useAssistantInstructions } from "@assistant-ui/react";
import { Thread } from "@/components/assistant-ui/thread";
import {
  <PERSON>barInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { Separator } from "@/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { InstrumentManager, type DeviceInfo } from "@/lib/instrument-runtime";
import { ImagePreview } from "@/components/assistant-ui/image-preview";

// Component that provides system instructions
const SystemInstructionsProvider = ({ children }: { children: React.ReactNode }) => {
  const DEFAULT_SYSTEM_PROMPT = `You are an AI assistant for "The Instrument" - a powerful screen capture and analysis system. You help users analyze screen captures from iOS devices and provide insights about what you see.

Key capabilities:
- Analyze screen captures from iOS devices
- Extract text, UI elements, and visual information
- Provide structured summaries and insights
- Help with debugging, UI analysis, and content extraction
- Maintain conversation context across multiple captures

Be helpful, concise, and focus on providing actionable insights from the visual content you analyze.`;

  useAssistantInstructions(DEFAULT_SYSTEM_PROMPT);
  return <>{children}</>;
};

export const Assistant = () => {
  // Config - using different ports to avoid conflicts with Next.js dev server
  const BACKEND_URL = "http://localhost:8080";
  const WS_URL = "ws://localhost:8081";

  // Default system prompt for The Instrument
  const DEFAULT_SYSTEM_PROMPT = `You are an AI assistant for "The Instrument" - a powerful screen capture and analysis system. You help users analyze screen captures from iOS devices and provide insights about what you see.

Key capabilities:
- Analyze screen captures from iOS devices
- Extract text, UI elements, and visual information
- Provide structured summaries and insights
- Help with debugging, UI analysis, and content extraction
- Maintain conversation context across multiple captures

Be helpful, concise, and focus on providing actionable insights from the visual content you analyze.`;

  // Instrument state
  const [connectionStatus, setConnectionStatus] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [images, setImages] = useState<string[]>([]);
  const [instrumentManager, setInstrumentManager] = useState<InstrumentManager | null>(null);

  // Initialize InstrumentManager once
  useEffect(() => {
    const manager = new InstrumentManager({
      backendUrl: BACKEND_URL,
      wsUrl: WS_URL,
      onDeviceStatusChange: (connected, info) => {
        setConnectionStatus(connected);
        setDeviceInfo(info);
      },
      onImagesReceived: (imgs) => setImages(imgs),
      systemPrompt: DEFAULT_SYSTEM_PROMPT,
    });

    setInstrumentManager(manager);
    return () => manager.destroy();
  }, []);

  // Fallback adapter while initializing
  const defaultAdapter = useMemo(
    () => ({
      async *run() {
        yield { content: [{ type: "text", text: "Initializing..." }] } as any;
      },
    }),
    []
  );

  // Local runtime backed by our Instrument adapter with history support
  const runtime = useLocalRuntime(
    instrumentManager ? instrumentManager.createAdapter() : (defaultAdapter as any),
    {
      adapters: {
        history: instrumentManager ? instrumentManager.createHistoryAdapter() : undefined,
      },
    }
  );

  // Handlers
  const handleCapture = () => instrumentManager?.captureImages(1);
  const handleSend = () => instrumentManager?.sendToDevice();

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <SidebarProvider>
        <div className="flex h-dvh w-full pr-0.5">
          <AppSidebar />
          <SidebarInset>
            <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
              <SidebarTrigger />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="https://www.assistant-ui.com/docs/getting-started" target="_blank" rel="noopener noreferrer">
                      Build Your Own ChatGPT UX
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Starter Template</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Device status chip */}
              <div className="ml-auto rounded-full px-3 py-1 text-sm border flex items-center gap-2">
                <span className={`inline-block size-2 rounded-full ${connectionStatus ? "bg-green-500" : "bg-red-500"}`} />
                <span>{connectionStatus ? "Device Connected" : "Device Disconnected"}</span>
                {deviceInfo && (
                  <span className="text-muted-foreground">({deviceInfo.name} - {deviceInfo.host}:{deviceInfo.port})</span>
                )}
              </div>
            </header>
            <div className="relative flex-1 overflow-hidden">
              {/* Floating controls */}
              <div className="pointer-events-none absolute inset-0 z-10">
                <div className="pointer-events-auto absolute right-4 top-4 flex flex-col gap-3">
                  <button
                    onClick={handleCapture}
                    disabled={!connectionStatus}
                    className="rounded-lg bg-emerald-600 text-white px-6 py-3 text-base font-medium shadow-lg hover:bg-emerald-500 disabled:opacity-50 transition-colors"
                  >
                    Capture
                  </button>
                  <button
                    onClick={handleSend}
                    disabled={!connectionStatus}
                    className="rounded-lg bg-amber-600 text-white px-6 py-3 text-base font-medium shadow-lg hover:bg-amber-500 disabled:opacity-50 transition-colors"
                  >
                    Send
                  </button>
                </div>
                <div className="pointer-events-auto absolute left-4 bottom-4">
                  <ImagePreview images={images} />
                </div>
              </div>

              {/* Chat thread with system instructions */}
              <SystemInstructionsProvider>
                <Thread />
              </SystemInstructionsProvider>
            </div>
          </SidebarInset>
        </div>
      </SidebarProvider>
    </AssistantRuntimeProvider>
  );
};
